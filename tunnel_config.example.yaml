# SSH Tunnel Configuration Example (YAML format)
ssh_tunnel:
  ssh_host: "your-jump-server.com"
  ssh_port: 22
  ssh_username: "your-username"
  # Use either password or private key authentication
  ssh_password: "your-password"
  # ssh_private_key_path: "/path/to/your/private/key"
  
  # Remote MySQL server details (on the jump server side)
  remote_bind_address: "127.0.0.1"  # MySQL server address from jump server perspective
  remote_bind_port: 3306             # MySQL server port
  
  # Local tunnel endpoint
  local_bind_address: "127.0.0.1"
  local_bind_port: 3307              # Local port to bind tunnel to

# MySQL Connection Configuration
mysql:
  host: "127.0.0.1"                  # Use local tunnel endpoint
  port: 3307                         # Use local tunnel port
  user: "your-mysql-username"
  password: "your-mysql-password"
  database: "your-database-name"
  charset: "utf8mb4"

# Connection Settings
connection:
  timeout: 30                        # Connection timeout in seconds
  autocommit: true                   # Auto-commit transactions
  max_retries: 3                     # Maximum connection retry attempts
