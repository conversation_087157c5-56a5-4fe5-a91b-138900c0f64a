#!/usr/bin/env python3
"""
Example usage of MySQL MCP Server

This script demonstrates how to use the MySQL MCP server tools
in a programmatic way for testing and development.
"""

import json
import tempfile
from pathlib import Path

# Import the tools from mysql.py
from mysql import (
    load_tunnel_config, 
    connect_mysql, 
    execute_query, 
    show_tables, 
    describe_table, 
    disconnect
)

def create_example_config():
    """Create an example configuration file"""
    config = {
        "ssh_tunnel": {
            "ssh_host": "your-jump-server.com",
            "ssh_port": 22,
            "ssh_username": "your-username",
            "ssh_password": "your-password",
            # "ssh_private_key_path": "/path/to/your/private/key",
            "remote_bind_address": "127.0.0.1",
            "remote_bind_port": 3306,
            "local_bind_address": "127.0.0.1",
            "local_bind_port": 3307
        },
        "mysql": {
            "host": "127.0.0.1",
            "port": 3307,
            "user": "your-mysql-username",
            "password": "your-mysql-password",
            "database": "your-database-name",
            "charset": "utf8mb4"
        },
        "connection": {
            "timeout": 30,
            "autocommit": True,
            "max_retries": 3
        }
    }
    
    # Write to a temporary file for demonstration
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(config, f, indent=2)
        return f.name

def demonstrate_usage():
    """Demonstrate the usage of MySQL MCP tools"""
    print("MySQL MCP Server Usage Example")
    print("=" * 50)
    
    # Step 1: Create and load configuration
    print("\n1. Loading configuration...")
    config_path = create_example_config()
    try:
        result = load_tunnel_config(config_path)
        print(f"   Result: {result}")
    finally:
        # Clean up the temporary config file
        Path(config_path).unlink()
    
    # Step 2: Attempt to connect (will fail without real server)
    print("\n2. Attempting to connect to MySQL...")
    result = connect_mysql(use_tunnel=True)
    print(f"   Result: {result}")
    
    # Step 3: Try to execute a query (will fail without connection)
    print("\n3. Attempting to execute a query...")
    result = execute_query("SELECT VERSION()", fetch_results=True, limit=10)
    print(f"   Result: {result}")
    
    # Step 4: Try to show tables (will fail without connection)
    print("\n4. Attempting to show tables...")
    result = show_tables()
    print(f"   Result: {result}")
    
    # Step 5: Try to describe a table (will fail without connection)
    print("\n5. Attempting to describe a table...")
    result = describe_table("users")
    print(f"   Result: {result}")
    
    # Step 6: Disconnect
    print("\n6. Disconnecting...")
    result = disconnect()
    print(f"   Result: {result}")
    
    print("\n" + "=" * 50)
    print("Example completed!")
    print("\nTo use with a real MySQL server:")
    print("1. Edit tunnel_config.example.json with your actual server details")
    print("2. Save it as tunnel_config.json")
    print("3. Start the MCP server: uv run python mysql.py")
    print("4. Connect with an MCP client or use the tools programmatically")

def show_tool_schemas():
    """Show the schema for each tool"""
    print("\nTool Schemas:")
    print("-" * 30)
    
    tools_info = [
        {
            "name": "load_tunnel_config",
            "description": "Load SSH tunnel and MySQL configuration from a local file",
            "parameters": {
                "config_path": "string - Path to the configuration file (JSON or YAML)"
            }
        },
        {
            "name": "connect_mysql", 
            "description": "Establish connection to MySQL through SSH tunnel",
            "parameters": {
                "use_tunnel": "boolean - Whether to use SSH tunnel (default: true)"
            }
        },
        {
            "name": "execute_query",
            "description": "Execute a SQL query on the connected MySQL database", 
            "parameters": {
                "query": "string - SQL query to execute",
                "fetch_results": "boolean - Whether to fetch and return results (default: true)",
                "limit": "integer - Maximum number of rows to return (default: 100)"
            }
        },
        {
            "name": "show_tables",
            "description": "Show all tables in the current database",
            "parameters": "None"
        },
        {
            "name": "describe_table",
            "description": "Describe the structure of a specific table",
            "parameters": {
                "table_name": "string - Name of the table to describe"
            }
        },
        {
            "name": "disconnect",
            "description": "Close MySQL connection and SSH tunnel", 
            "parameters": "None"
        }
    ]
    
    for tool in tools_info:
        print(f"\n{tool['name']}:")
        print(f"  Description: {tool['description']}")
        if tool['parameters'] == "None":
            print(f"  Parameters: None")
        else:
            print(f"  Parameters:")
            for param, desc in tool['parameters'].items():
                print(f"    - {param}: {desc}")

if __name__ == "__main__":
    demonstrate_usage()
    show_tool_schemas()
