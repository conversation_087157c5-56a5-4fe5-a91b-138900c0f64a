#!/usr/bin/env python3
"""
Test script for MySQL MCP Server

This script tests the MySQL MCP server functionality without requiring
an actual MySQL connection.
"""

import json
import tempfile
from pathlib import Path

# Import the tools directly for testing
from mysql import load_tunnel_config, connect_mysql, disconnect

def test_config_loading():
    """Test configuration loading functionality"""
    print("Testing configuration loading...")
    
    # Create a temporary config file
    test_config = {
        "ssh_tunnel": {
            "ssh_host": "test-server.com",
            "ssh_port": 22,
            "ssh_username": "testuser",
            "ssh_password": "testpass",
            "remote_bind_address": "127.0.0.1",
            "remote_bind_port": 3306,
            "local_bind_address": "127.0.0.1",
            "local_bind_port": 3307
        },
        "mysql": {
            "host": "127.0.0.1",
            "port": 3307,
            "user": "mysql_user",
            "password": "mysql_pass",
            "database": "test_db",
            "charset": "utf8mb4"
        },
        "connection": {
            "timeout": 30,
            "autocommit": True,
            "max_retries": 3
        }
    }
    
    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_config, f, indent=2)
        temp_config_path = f.name
    
    try:
        # Test loading the config
        result = load_tunnel_config(temp_config_path)
        print(f"✓ Config loading result: {result}")
        
        # Test loading non-existent file
        result = load_tunnel_config("non_existent_file.json")
        print(f"✓ Non-existent file result: {result}")
        
    finally:
        # Clean up
        Path(temp_config_path).unlink()

def test_connection_without_server():
    """Test connection functionality (will fail without actual server)"""
    print("\nTesting connection functionality...")
    
    # This should fail gracefully since we don't have a real config loaded
    result = connect_mysql(use_tunnel=False)
    print(f"✓ Connection without config result: {result}")
    
    # Test disconnect
    result = disconnect()
    print(f"✓ Disconnect result: {result}")

def test_yaml_config():
    """Test YAML configuration loading"""
    print("\nTesting YAML configuration loading...")
    
    yaml_config = """
ssh_tunnel:
  ssh_host: "test-server.com"
  ssh_port: 22
  ssh_username: "testuser"
  ssh_password: "testpass"
  remote_bind_address: "127.0.0.1"
  remote_bind_port: 3306
  local_bind_address: "127.0.0.1"
  local_bind_port: 3307

mysql:
  host: "127.0.0.1"
  port: 3307
  user: "mysql_user"
  password: "mysql_pass"
  database: "test_db"
  charset: "utf8mb4"

connection:
  timeout: 30
  autocommit: true
  max_retries: 3
"""
    
    # Write to temporary YAML file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(yaml_config)
        temp_yaml_path = f.name
    
    try:
        # Test loading the YAML config
        result = load_tunnel_config(temp_yaml_path)
        print(f"✓ YAML config loading result: {result}")
        
    finally:
        # Clean up
        Path(temp_yaml_path).unlink()

def main():
    """Run all tests"""
    print("MySQL MCP Server Test Suite")
    print("=" * 40)
    
    try:
        test_config_loading()
        test_connection_without_server()
        test_yaml_config()
        
        print("\n" + "=" * 40)
        print("✓ All tests completed successfully!")
        print("\nNote: Connection tests failed as expected since no real MySQL server is configured.")
        print("To test with a real MySQL server:")
        print("1. Copy tunnel_config.example.json to tunnel_config.json")
        print("2. Edit the configuration with your actual server details")
        print("3. Run: uv run python mysql.py")
        print("4. Connect with an MCP client")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
