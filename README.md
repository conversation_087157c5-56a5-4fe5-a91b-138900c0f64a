# MySQL MCP Server with SSH Tunnel Support

这是一个支持SSH隧道的MySQL MCP (Model Context Protocol) 服务器，可以安全地连接到远程MySQL数据库并执行查询操作。

## 功能特性

- 🔐 **SSH隧道支持**: 通过SSH隧道安全连接到远程MySQL服务器
- 📁 **配置文件支持**: 支持JSON和YAML格式的配置文件
- 🔍 **数据库查询**: 执行SQL查询并返回格式化结果
- 📊 **表结构查看**: 查看数据库表列表和表结构
- 🛡️ **安全连接**: 支持密码和私钥认证
- ⚡ **异步操作**: 基于asyncio的高性能异步处理

## 安装依赖

项目使用uv进行依赖管理，所有必要的依赖已经配置好：

```bash
uv sync
```

## 配置文件

### 1. 复制示例配置文件

```bash
# JSON格式
cp tunnel_config.example.json tunnel_config.json

# 或者YAML格式
cp tunnel_config.example.yaml tunnel_config.yaml
```

### 2. 编辑配置文件

根据你的实际环境修改配置文件：

#### SSH隧道配置
```json
{
  "ssh_tunnel": {
    "ssh_host": "your-jump-server.com",
    "ssh_port": 22,
    "ssh_username": "your-username",
    "ssh_password": "your-password",
    // 或者使用私钥认证
    "ssh_private_key_path": "/path/to/your/private/key",
    "remote_bind_address": "127.0.0.1",
    "remote_bind_port": 3306,
    "local_bind_address": "127.0.0.1",
    "local_bind_port": 3307
  }
}
```

#### MySQL配置
```json
{
  "mysql": {
    "host": "127.0.0.1",
    "port": 3307,
    "user": "your-mysql-username",
    "password": "your-mysql-password",
    "database": "your-database-name",
    "charset": "utf8mb4"
  }
}
```

## 使用方法

### 1. 启动MCP服务器

```bash
python mysql.py
```

### 2. 可用工具

#### `load_tunnel_config`
加载SSH隧道和MySQL配置文件
```json
{
  "config_path": "tunnel_config.json"
}
```

#### `connect_mysql`
建立MySQL连接（可选择是否使用SSH隧道）
```json
{
  "use_tunnel": true
}
```

#### `execute_query`
执行SQL查询
```json
{
  "query": "SELECT * FROM users LIMIT 10",
  "fetch_results": true,
  "limit": 100
}
```

#### `show_tables`
显示数据库中的所有表
```json
{}
```

#### `describe_table`
查看表结构
```json
{
  "table_name": "users"
}
```

#### `disconnect`
关闭MySQL连接和SSH隧道
```json
{}
```

## 使用示例

1. **加载配置**:
   ```
   load_tunnel_config("tunnel_config.json")
   ```

2. **连接数据库**:
   ```
   connect_mysql(use_tunnel=true)
   ```

3. **查看表列表**:
   ```
   show_tables()
   ```

4. **查看表结构**:
   ```
   describe_table("users")
   ```

5. **执行查询**:
   ```
   execute_query("SELECT COUNT(*) FROM users")
   ```

6. **断开连接**:
   ```
   disconnect()
   ```

## 安全注意事项

- 🔒 配置文件包含敏感信息，请确保文件权限设置正确
- 🚫 不要将包含真实密码的配置文件提交到版本控制系统
- 🔑 推荐使用SSH私钥认证而不是密码认证
- 🛡️ 确保SSH服务器和MySQL服务器的安全配置

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查SSH服务器地址、端口和认证信息
   - 确认SSH服务器允许端口转发
   - 验证网络连接

2. **MySQL连接失败**
   - 检查MySQL服务器地址和端口
   - 验证用户名和密码
   - 确认数据库存在且用户有访问权限

3. **配置文件错误**
   - 验证JSON/YAML语法
   - 检查必需字段是否存在
   - 确认文件路径正确

## 开发

### 项目结构
```
.
├── mysql.py                    # 主要的MCP服务器实现
├── tunnel_config.example.json  # JSON配置文件示例
├── tunnel_config.example.yaml  # YAML配置文件示例
├── pyproject.toml              # 项目依赖配置
└── README.md                   # 项目文档
```

### 依赖项
- `mcp[cli]>=1.12.2` - MCP协议支持
- `pymysql==1.1.1` - MySQL连接器
- `sshtunnel==0.4.0` - SSH隧道支持
- `pyyaml==6.0.2` - YAML配置文件支持

## 许可证

本项目基于MIT许可证开源。